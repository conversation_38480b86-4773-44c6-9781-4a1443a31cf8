/**
 * Unity FakeStore Complete Automation Script
 * Combines Java hooks and memory manipulation for comprehensive IAP bypass
 */

console.log("[+] FakeStore Complete Automation Loading...");

// Master configuration
const AUTOMATION_CONFIG = {
    // Purchase automation
    AUTO_COMPLETE_PURCHASES: true,
    BYPASS_PURCHASE_DIALOGS: true,
    FORCE_PURCHASE_SUCCESS: true,
    
    // UI automation  
    SILENT_MODE: true,
    SKIP_CONFIRMATIONS: true,
    
    // Logging
    LOG_ALL_PURCHASES: true,
    LOG_MEMORY_OPERATIONS: false,
    
    // Advanced options
    HOOK_STORE_SELECTION: true,  // Force FakeStore usage
    PATCH_REAL_STORES: true,     // Make real stores behave like FakeStore
    CONTINUOUS_MONITORING: true
};

var hookedMethods = [];
var fakeStoreInstances = [];
var purchaseQueue = [];

// Main initialization
Java.perform(function() {
    console.log("[+] Starting comprehensive FakeStore automation...");
    
    try {
        // 1. Hook FakeStore directly
        hookFakeStoreClass();
        
        // 2. Hook store selection system
        if (AUTOMATION_CONFIG.HOOK_STORE_SELECTION) {
            hookStoreSelection();
        }
        
        // 3. Hook real store classes to behave like FakeStore
        if (AUTOMATION_CONFIG.PATCH_REAL_STORES) {
            hookRealStores();
        }
        
        // 4. Set up purchase monitoring
        if (AUTOMATION_CONFIG.CONTINUOUS_MONITORING) {
            setupPurchaseMonitoring();
        }
        
        console.log("[+] All automation hooks installed!");
        
    } catch (error) {
        console.log("[-] Error in main setup: " + error.message);
        // Fallback to memory-only approach
        setupMemoryOnlyAutomation();
    }
});

function hookFakeStoreClass() {
    try {
        var FakeStore = Java.use("UnityEngine.Purchasing.FakeStore");
        
        // Override Purchase method for complete automation
        FakeStore.Purchase.overload('java.lang.String', 'java.lang.String').implementation = function(productJSON, developerPayload) {
            var product = JSON.parse(productJSON);
            
            if (AUTOMATION_CONFIG.LOG_ALL_PURCHASES) {
                console.log("[+] AUTO-PURCHASE: " + product.id + " (" + product.type + ")");
            }
            
            if (AUTOMATION_CONFIG.AUTO_COMPLETE_PURCHASES) {
                // Add to purchase queue for instant completion
                purchaseQueue.push({
                    id: product.id,
                    json: productJSON,
                    payload: developerPayload,
                    timestamp: Date.now()
                });
                
                // Complete immediately
                completePurchaseInstantly(product, developerPayload);
                return;
            }
            
            // Call original if automation disabled
            this.Purchase(productJSON, developerPayload);
        };
        
        // Override FakePurchase for guaranteed success
        FakeStore.FakePurchase.implementation = function(product, developerPayload) {
            console.log("[+] FakePurchase intercepted: " + product.id);
            
            // Always succeed
            completePurchaseInstantly(product, developerPayload);
        };
        
        // Override Initialize to set optimal configuration
        FakeStore.Initialize.implementation = function(biller) {
            console.log("[+] FakeStore Initialize - configuring for automation");
            
            // Call original first
            this.Initialize(biller);
            
            // Configure for automation
            if (AUTOMATION_CONFIG.SILENT_MODE) {
                this.UIMode.value = 0; // Silent mode
            }
            
            // Store instance reference
            fakeStoreInstances.push(this);
        };
        
        // Override StartUI to always approve
        if (AUTOMATION_CONFIG.BYPASS_PURCHASE_DIALOGS) {
            FakeStore.StartUI.implementation = function(model, dialogType, callback) {
                console.log("[+] StartUI bypassed - auto-approving");
                
                // Immediately approve
                if (callback) {
                    callback.invoke(true, model);
                }
                return true;
            };
        }
        
        // Override RestoreTransactions for instant success
        FakeStore.RestoreTransactions.implementation = function(callback) {
            console.log("[+] RestoreTransactions - instant success");
            
            this.restoreCalled.value = true;
            
            if (callback) {
                callback.invoke(true, "All purchases restored successfully");
            }
        };
        
        hookedMethods.push("FakeStore");
        console.log("[+] FakeStore class hooks installed");
        
    } catch (error) {
        console.log("[-] Failed to hook FakeStore: " + error.message);
    }
}

function completePurchaseInstantly(product, developerPayload) {
    try {
        console.log("[+] Completing purchase instantly: " + product.id);
        
        // Find active FakeStore instance
        var activeStore = fakeStoreInstances[fakeStoreInstances.length - 1];
        if (!activeStore) {
            console.log("[-] No active FakeStore instance found");
            return;
        }
        
        // Add to purchased products
        var purchasedList = activeStore.m_PurchasedProducts.value;
        if (!purchasedList.contains(product.id)) {
            purchasedList.add(product.id);
        }
        
        // Set success flags
        activeStore.purchaseCalled.value = true;
        
        // Generate fake receipt
        var receipt = JSON.stringify({
            productId: product.id,
            transactionId: "fake_" + Date.now(),
            purchaseTime: Date.now(),
            developerPayload: developerPayload,
            signature: "fake_signature"
        });
        
        // Trigger success callback
        var biller = activeStore.m_Biller.value;
        if (biller) {
            biller.OnPurchaseSucceeded(product.id, receipt, "fake_transaction_" + Date.now());
            console.log("[+] Purchase success callback triggered");
        }
        
    } catch (error) {
        console.log("[-] Failed to complete purchase instantly: " + error.message);
    }
}

function hookStoreSelection() {
    try {
        // Hook Unity's store selection to always use FakeStore
        var UnityPurchasing = Java.use("UnityEngine.Purchasing.UnityPurchasing");
        
        // This might need adjustment based on actual Unity IAP implementation
        if (UnityPurchasing.Initialize) {
            UnityPurchasing.Initialize.implementation = function(listener, builder) {
                console.log("[+] UnityPurchasing Initialize - forcing FakeStore");
                
                // Force FakeStore usage by modifying builder
                // Implementation depends on actual Unity IAP structure
                
                return this.Initialize(listener, builder);
            };
        }
        
        console.log("[+] Store selection hooks installed");
        
    } catch (error) {
        console.log("[-] Failed to hook store selection: " + error.message);
    }
}

function hookRealStores() {
    try {
        // Hook Google Play Store
        try {
            var GooglePlayStore = Java.use("UnityEngine.Purchasing.GooglePlayStore");
            
            GooglePlayStore.Purchase.implementation = function(productJSON, developerPayload) {
                console.log("[+] GooglePlayStore Purchase redirected to FakeStore behavior");
                
                var product = JSON.parse(productJSON);
                completePurchaseInstantly(product, developerPayload);
            };
            
            hookedMethods.push("GooglePlayStore");
            
        } catch (e) {
            console.log("[-] GooglePlayStore not found or already hooked");
        }
        
        // Hook Apple App Store
        try {
            var AppleAppStore = Java.use("UnityEngine.Purchasing.AppleAppStore");
            
            AppleAppStore.Purchase.implementation = function(productJSON, developerPayload) {
                console.log("[+] AppleAppStore Purchase redirected to FakeStore behavior");
                
                var product = JSON.parse(productJSON);
                completePurchaseInstantly(product, developerPayload);
            };
            
            hookedMethods.push("AppleAppStore");
            
        } catch (e) {
            console.log("[-] AppleAppStore not found or already hooked");
        }
        
        console.log("[+] Real store hooks installed");
        
    } catch (error) {
        console.log("[-] Failed to hook real stores: " + error.message);
    }
}

function setupPurchaseMonitoring() {
    // Monitor purchase queue and auto-complete
    setInterval(function() {
        if (purchaseQueue.length > 0) {
            console.log("[+] Processing " + purchaseQueue.length + " queued purchases");
            
            purchaseQueue.forEach(function(purchase) {
                console.log("[+] Auto-completing queued purchase: " + purchase.id);
            });
            
            // Clear queue
            purchaseQueue = [];
        }
    }, 1000);
    
    console.log("[+] Purchase monitoring started");
}

function setupMemoryOnlyAutomation() {
    console.log("[+] Setting up memory-only automation fallback...");
    
    // Find Unity base address
    var baseAddr = Module.findBaseAddress("libil2cpp.so") || Module.findBaseAddress("GameAssembly.dll");
    if (!baseAddr) {
        console.log("[-] Could not find Unity base address for memory hooks");
        return;
    }
    
    console.log("[+] Unity base: " + baseAddr);
    
    // Hook purchase-related functions at memory level
    // This would need specific RVA addresses from the target game
    
    console.log("[+] Memory-only automation configured");
}

// Utility functions for runtime control
function enableAutomation() {
    AUTOMATION_CONFIG.AUTO_COMPLETE_PURCHASES = true;
    AUTOMATION_CONFIG.FORCE_PURCHASE_SUCCESS = true;
    console.log("[+] Automation enabled");
}

function disableAutomation() {
    AUTOMATION_CONFIG.AUTO_COMPLETE_PURCHASES = false;
    AUTOMATION_CONFIG.FORCE_PURCHASE_SUCCESS = false;
    console.log("[+] Automation disabled");
}

function simulatePurchase(productId, productType) {
    console.log("[+] Simulating purchase: " + productId);
    
    var fakeProduct = {
        id: productId,
        type: productType || "Consumable"
    };
    
    completePurchaseInstantly(fakeProduct, "simulated_payload");
}

function getAutomationStatus() {
    return {
        config: AUTOMATION_CONFIG,
        hookedMethods: hookedMethods,
        fakeStoreInstances: fakeStoreInstances.length,
        queuedPurchases: purchaseQueue.length
    };
}

// Export functions for runtime use
global.FakeStoreAutomation = {
    enable: enableAutomation,
    disable: disableAutomation,
    simulatePurchase: simulatePurchase,
    getStatus: getAutomationStatus,
    config: AUTOMATION_CONFIG
};

console.log("[+] FakeStore Complete Automation Loaded!");
console.log("[+] Use global.FakeStoreAutomation for runtime control");
console.log("[+] Hooked methods: " + hookedMethods.join(", "));
