/**
 * Dominations IL2CPP GoodyHutHelper Automation
 * Native memory hooks for Unity IL2CPP compiled game
 * Target: com.nexonm.dominations.adk (Android ARM64)
 */

console.log("[+] Dominations IL2CPP Automation Loading...");

// Configuration for automation preferences
const CONFIG = {
    INSTANT_COLLECTION: true,      // Hook GetCollectTime() to return 0
    AUTO_COMPLETE: true,           // Auto-complete collection processes
    BYPASS_COOLDOWNS: true,        // Hook GetCooldownTimeLeft() to return 0
    AUTO_CLEANUP: true,            // Enable automatic cleanup mechanisms
    LOG_OPERATIONS: true,          // Log all hooked operations
    CONTINUOUS_SCAN: true          // Continuously scan for new instances
};

// GoodyHutHelper RVA addresses from decompiled code
const RVA_ADDRESSES = {
    // Core collection methods
    GET_COLLECT_TIME: 0x209E904,        // GetCollectTime() -> float
    CAN_COLLECT: 0x209D258,             // CanCollect() -> bool
    START_COLLECT: 0x209D434,           // StartCollect() -> void
    FINISH_COLLECT: 0x209B924,          // FinishCollect() -> void
    FINISH_COLLECT_SPECIAL: 0x209D588,  // FinishCollectSpecial() -> void
    
    // Cooldown methods
    GET_COOLDOWN_TIME_LEFT: 0x209E9B0,  // GetCooldownTimeLeft() -> double
    GET_COOLDOWN_TIME: 0x209E9A4,       // GetCooldownTime() -> float
    
    // State methods
    IS_JOB_COMPLETE: 0x209B664,         // IsJobComplete() -> bool
    CAN_BUY_THROUGH: 0x209B6BC,         // CanBuyThrough() -> bool
    DO_JOB_BUY_THROUGH: 0x209B760,      // DoJobBuyThrough() -> void
    DO_FREE_BUY_THROUGH: 0x209C2B8,     // DoFreeBuyThrough() -> void
    
    // Configuration
    CONFIG_METHOD: 0x209B570,           // Config() -> GoodyHutHelperConfig
    
    // Constructor
    CONSTRUCTOR: 0x209B470              // GoodyHutHelper(EntityController ec)
};

// GoodyHutHelperConfig offsets
const CONFIG_OFFSETS = {
    CLEANUP_FLAG: 0x30,     // bool cleanUp at offset 0x30
    MAX_EXPLORATIONS: 0x10, // FuzzedInt fuzzedMaxExp
    NUM_CITIZENS: 0x18,     // FuzzedInt fuzzedNumCitizens
    SELL_PRICE: 0x34,       // FuzzedInt fuzzedSellPrice
    BONUS_AMOUNT: 0x48      // FuzzedInt fuzzedBonusAmount
};

var il2cppBase = null;
var hookedMethods = [];
var goodyHutInstances = [];

function initializeIL2CPPHooks() {
    console.log("[+] Initializing IL2CPP hooks for Dominations...");
    
    // Find libil2cpp.so base address
    il2cppBase = Module.findBaseAddress("libil2cpp.so");
    if (!il2cppBase) {
        console.log("[-] libil2cpp.so not found, trying alternative names...");
        il2cppBase = Module.findBaseAddress("libunity.so") || 
                     Module.findBaseAddress("libmain.so");
    }
    
    if (!il2cppBase) {
        console.log("[-] Could not find IL2CPP library base address");
        return false;
    }
    
    console.log("[+] IL2CPP base address: " + il2cppBase);
    
    // Set up method hooks
    setupGoodyHutHooks();
    
    // Set up instance scanning
    if (CONFIG.CONTINUOUS_SCAN) {
        setupInstanceScanning();
    }
    
    return true;
}

function setupGoodyHutHooks() {
    console.log("[+] Setting up GoodyHutHelper method hooks...");
    
    try {
        // Hook GetCollectTime for instant collection
        if (CONFIG.INSTANT_COLLECTION) {
            hookGetCollectTime();
        }
        
        // Hook cooldown methods
        if (CONFIG.BYPASS_COOLDOWNS) {
            hookCooldownMethods();
        }
        
        // Hook collection flow methods
        if (CONFIG.AUTO_COMPLETE) {
            hookCollectionFlow();
        }
        
        // Hook configuration methods
        if (CONFIG.AUTO_CLEANUP) {
            hookConfigurationMethods();
        }
        
        console.log("[+] GoodyHutHelper hooks installed successfully");
        
    } catch (error) {
        console.log("[-] Error setting up hooks: " + error.message);
        // Try fallback signature scanning
        setupFallbackHooks();
    }
}

function hookGetCollectTime() {
    try {
        var getCollectTimeAddr = il2cppBase.add(RVA_ADDRESSES.GET_COLLECT_TIME);

        Interceptor.attach(getCollectTimeAddr, {
            onEnter: function(args) {
                // args[0] = this pointer (GoodyHutHelper instance)
                this.goodyHutPtr = args[0];

                if (CONFIG.LOG_OPERATIONS) {
                    console.log("[+] GetCollectTime() called on instance: " + this.goodyHutPtr);
                }

                // Register this instance
                registerGoodyHutInstance(this.goodyHutPtr);
            },
            onLeave: function(retval) {
                if (CONFIG.INSTANT_COLLECTION) {
                    // Force return 0.0f for instant collection
                    // Create proper float representation of 0.0
                    var zeroFloat = Memory.alloc(4);
                    zeroFloat.writeFloat(0.0);
                    retval.replace(zeroFloat.readU32());

                    if (CONFIG.LOG_OPERATIONS) {
                        console.log("[+] GetCollectTime() forced to return 0.0 for instant collection");
                    }
                }
            }
        });

        hookedMethods.push("GetCollectTime");
        console.log("[+] GetCollectTime() hook installed for instant collection");

    } catch (error) {
        console.log("[-] Failed to hook GetCollectTime: " + error.message);
    }
}

function hookCooldownMethods() {
    try {
        // Hook GetCooldownTimeLeft
        var getCooldownTimeLeftAddr = il2cppBase.add(RVA_ADDRESSES.GET_COOLDOWN_TIME_LEFT);
        
        Interceptor.attach(getCooldownTimeLeftAddr, {
            onEnter: function(args) {
                this.goodyHutPtr = args[0];
                
                if (CONFIG.LOG_OPERATIONS) {
                    console.log("[+] GetCooldownTimeLeft() called");
                }
            },
            onLeave: function(retval) {
                if (CONFIG.BYPASS_COOLDOWNS) {
                    // Force return 0.0 (double) for no cooldown
                    retval.replace(ptr(0x0));
                    
                    if (CONFIG.LOG_OPERATIONS) {
                        console.log("[+] GetCooldownTimeLeft() forced to return 0.0");
                    }
                }
            }
        });
        
        // Hook GetCooldownTime
        var getCooldownTimeAddr = il2cppBase.add(RVA_ADDRESSES.GET_COOLDOWN_TIME);
        
        Interceptor.attach(getCooldownTimeAddr, {
            onLeave: function(retval) {
                if (CONFIG.BYPASS_COOLDOWNS) {
                    retval.replace(ptr(0x0)); // 0.0f
                    
                    if (CONFIG.LOG_OPERATIONS) {
                        console.log("[+] GetCooldownTime() forced to return 0.0");
                    }
                }
            }
        });
        
        hookedMethods.push("CooldownMethods");
        console.log("[+] Cooldown bypass hooks installed");
        
    } catch (error) {
        console.log("[-] Failed to hook cooldown methods: " + error.message);
    }
}

function hookCollectionFlow() {
    try {
        // Hook CanCollect to always return true
        var canCollectAddr = il2cppBase.add(RVA_ADDRESSES.CAN_COLLECT);
        
        Interceptor.attach(canCollectAddr, {
            onEnter: function(args) {
                this.goodyHutPtr = args[0];
                
                if (CONFIG.LOG_OPERATIONS) {
                    console.log("[+] CanCollect() called on: " + this.goodyHutPtr);
                }
            },
            onLeave: function(retval) {
                if (CONFIG.AUTO_COMPLETE) {
                    // Force return true (1)
                    retval.replace(ptr(0x1));
                    
                    if (CONFIG.LOG_OPERATIONS) {
                        console.log("[+] CanCollect() forced to return true");
                    }
                }
            }
        });
        
        // Hook StartCollect for automatic triggering
        var startCollectAddr = il2cppBase.add(RVA_ADDRESSES.START_COLLECT);
        
        Interceptor.attach(startCollectAddr, {
            onEnter: function(args) {
                this.goodyHutPtr = args[0];
                
                if (CONFIG.LOG_OPERATIONS) {
                    console.log("[+] StartCollect() called - auto-completing...");
                }
            },
            onLeave: function(retval) {
                if (CONFIG.AUTO_COMPLETE) {
                    // Immediately call FinishCollect after StartCollect
                    setTimeout(() => {
                        triggerFinishCollect(this.goodyHutPtr);
                    }, 100); // Small delay to ensure proper state
                }
            }
        });
        
        // Hook FinishCollect for logging
        var finishCollectAddr = il2cppBase.add(RVA_ADDRESSES.FINISH_COLLECT);
        
        Interceptor.attach(finishCollectAddr, {
            onEnter: function(args) {
                if (CONFIG.LOG_OPERATIONS) {
                    console.log("[+] FinishCollect() called - collection completed!");
                }
            }
        });
        
        hookedMethods.push("CollectionFlow");
        console.log("[+] Collection flow hooks installed");
        
    } catch (error) {
        console.log("[-] Failed to hook collection flow: " + error.message);
    }
}

function hookConfigurationMethods() {
    try {
        // Hook Config() method to manipulate GoodyHutHelperConfig
        var configAddr = il2cppBase.add(RVA_ADDRESSES.CONFIG_METHOD);
        
        Interceptor.attach(configAddr, {
            onEnter: function(args) {
                this.goodyHutPtr = args[0];
            },
            onLeave: function(retval) {
                if (CONFIG.AUTO_CLEANUP && retval && !retval.isNull()) {
                    // Manipulate the returned config object
                    patchGoodyHutConfig(retval);
                    
                    if (CONFIG.LOG_OPERATIONS) {
                        console.log("[+] GoodyHutHelperConfig patched for auto-cleanup");
                    }
                }
            }
        });
        
        hookedMethods.push("Configuration");
        console.log("[+] Configuration hooks installed");
        
    } catch (error) {
        console.log("[-] Failed to hook configuration methods: " + error.message);
    }
}

function patchGoodyHutConfig(configPtr) {
    try {
        // Set cleanUp flag to true (offset 0x30)
        configPtr.add(CONFIG_OFFSETS.CLEANUP_FLAG).writeU8(1);
        
        if (CONFIG.LOG_OPERATIONS) {
            console.log("[+] Config cleanUp flag set to true at: " + configPtr.add(CONFIG_OFFSETS.CLEANUP_FLAG));
        }
        
    } catch (error) {
        console.log("[-] Failed to patch GoodyHutConfig: " + error.message);
    }
}

function triggerFinishCollect(goodyHutPtr) {
    try {
        // Call FinishCollect method directly
        var finishCollectAddr = il2cppBase.add(RVA_ADDRESSES.FINISH_COLLECT);
        var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
        
        finishCollectFunc(goodyHutPtr);
        
        if (CONFIG.LOG_OPERATIONS) {
            console.log("[+] FinishCollect() triggered automatically for: " + goodyHutPtr);
        }
        
    } catch (error) {
        console.log("[-] Failed to trigger FinishCollect: " + error.message);
    }
}

function registerGoodyHutInstance(ptr) {
    // Avoid duplicates
    for (var i = 0; i < goodyHutInstances.length; i++) {
        if (goodyHutInstances[i].equals(ptr)) {
            return;
        }
    }
    
    goodyHutInstances.push(ptr);
    
    if (CONFIG.LOG_OPERATIONS) {
        console.log("[+] Registered GoodyHutHelper instance: " + ptr);
        console.log("[+] Total instances tracked: " + goodyHutInstances.length);
    }
}

function setupInstanceScanning() {
    // Scan for GoodyHutHelper instances periodically
    setInterval(function() {
        if (CONFIG.LOG_OPERATIONS && goodyHutInstances.length > 0) {
            console.log("[+] Monitoring " + goodyHutInstances.length + " GoodyHutHelper instances");
        }
    }, 10000); // Every 10 seconds
}

function setupFallbackHooks() {
    console.log("[+] Setting up fallback signature scanning...");
    
    try {
        // Scan for method signatures if RVA addresses fail
        var il2cppModule = Process.getModuleByName("libil2cpp.so");
        
        // Look for GetCollectTime signature (returns float)
        var getCollectTimePattern = "?? ?? ?? ?? ?? ?? ?? ?? ?? ?? ?? ?? ?? ?? ?? ??"; // Placeholder
        
        Memory.scan(il2cppModule.base, il2cppModule.size, getCollectTimePattern, {
            onMatch: function(address, size) {
                console.log("[+] Potential GetCollectTime found at: " + address);
                // Verify and hook if valid
            },
            onComplete: function() {
                console.log("[+] Signature scanning completed");
            }
        });
        
    } catch (error) {
        console.log("[-] Fallback signature scanning failed: " + error.message);
    }
}

// Utility functions for runtime control
function enableInstantCollection() {
    CONFIG.INSTANT_COLLECTION = true;
    console.log("[+] Instant collection enabled");
}

function disableInstantCollection() {
    CONFIG.INSTANT_COLLECTION = false;
    console.log("[+] Instant collection disabled");
}

function forceCollectAll() {
    console.log("[+] Force collecting all tracked GoodyHut instances...");
    
    goodyHutInstances.forEach(function(instance) {
        try {
            triggerFinishCollect(instance);
        } catch (error) {
            console.log("[-] Failed to force collect instance: " + instance);
        }
    });
}

function getAutomationStatus() {
    return {
        config: CONFIG,
        hookedMethods: hookedMethods,
        trackedInstances: goodyHutInstances.length,
        il2cppBase: il2cppBase ? il2cppBase.toString() : "Not found"
    };
}

// Initialize the hooks
if (initializeIL2CPPHooks()) {
    console.log("[+] Dominations IL2CPP Automation loaded successfully!");
    
    // Export utility functions to globalThis for Frida compatibility
    globalThis.DominationsAutomation = {
        enableInstantCollection: enableInstantCollection,
        disableInstantCollection: disableInstantCollection,
        forceCollectAll: forceCollectAll,
        getStatus: getAutomationStatus,
        config: CONFIG
    };
    
    console.log("[+] Utility functions exported to globalThis.DominationsAutomation");
    console.log("[+] Hooked methods: " + hookedMethods.join(", "));
    
} else {
    console.log("[-] Failed to initialize Dominations IL2CPP Automation");
}
