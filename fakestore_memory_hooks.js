/**
 * Unity FakeStore Memory-Based Hooks
 * Direct memory manipulation for FakeStore automation when Java hooks fail
 */

console.log("[+] FakeStore Memory Hooks Loading...");

// Configuration
const MEMORY_CONFIG = {
    FAKESTORE_SIZE: 0x88, // Approximate size based on field offsets
    SCAN_RANGE: 0x10000000, // 256MB scan range
    AUTO_PATCH: true,
    CONTINUOUS_SCAN: true
};

// Known offsets from the decompiled code
const OFFSETS = {
    M_BILLER: 0x60,           // IStoreCallback m_Biller
    M_PURCHASED_PRODUCTS: 0x68, // List<string> m_PurchasedProducts  
    PURCHASE_CALLED: 0x70,     // bool purchaseCalled
    RESTORE_CALLED: 0x71,      // bool restoreCalled
    UNAVAILABLE_PRODUCT_ID: 0x78, // string unavailableProductId
    UI_MODE: 0x80              // FakeStoreUIMode UIMode
};

// RVA addresses from the dump (adjust based on actual base address)
const RVA_ADDRESSES = {
    FAKE_PURCHASE: 0x4496A90,
    PURCHASE: 0x44967BC,
    INITIALIZE: 0x4495B84,
    START_UI: 0x4497128, // Generic method, may need adjustment
    RESTORE_TRANSACTIONS: 0x4496EF4
};

var fakeStoreInstances = [];
var baseAddress = null;

function initializeMemoryHooks() {
    // Find libil2cpp base address
    baseAddress = Module.findBaseAddress("libil2cpp.so");
    if (!baseAddress) {
        baseAddress = Module.findBaseAddress("GameAssembly.dll"); // Windows Unity
    }
    
    if (!baseAddress) {
        console.log("[-] Could not find Unity native library base address");
        return false;
    }
    
    console.log("[+] Unity base address: " + baseAddress);
    
    // Set up method hooks
    setupMethodHooks();
    
    // Start instance scanning
    if (MEMORY_CONFIG.CONTINUOUS_SCAN) {
        scanForFakeStoreInstances();
        setInterval(scanForFakeStoreInstances, 5000); // Scan every 5 seconds
    }
    
    return true;
}

function setupMethodHooks() {
    console.log("[+] Setting up method hooks...");
    
    try {
        // Hook FakePurchase method
        var fakePurchaseAddr = baseAddress.add(RVA_ADDRESSES.FAKE_PURCHASE);
        Interceptor.attach(fakePurchaseAddr, {
            onEnter: function(args) {
                console.log("[+] FakePurchase method called");
                
                // args[0] = this pointer (FakeStore instance)
                // args[1] = ProductDefinition
                // args[2] = developerPayload
                
                this.fakeStorePtr = args[0];
                this.productPtr = args[1];
                
                if (MEMORY_CONFIG.AUTO_PATCH) {
                    // Force purchase success by manipulating the instance
                    patchFakeStoreForSuccess(this.fakeStorePtr);
                }
                
                // Log product information if possible
                try {
                    var productId = this.productPtr.add(0x10).readPointer().readUtf8String();
                    console.log("[+] Purchasing product: " + productId);
                } catch (e) {
                    console.log("[+] Could not read product ID");
                }
            },
            onLeave: function(retval) {
                console.log("[+] FakePurchase completed");
                
                if (this.fakeStorePtr) {
                    // Ensure success flags are set
                    this.fakeStorePtr.add(OFFSETS.PURCHASE_CALLED).writeU8(1);
                }
            }
        });
        
        // Hook Purchase method (entry point)
        var purchaseAddr = baseAddress.add(RVA_ADDRESSES.PURCHASE);
        Interceptor.attach(purchaseAddr, {
            onEnter: function(args) {
                console.log("[+] Purchase method called");
                this.fakeStorePtr = args[0];
                
                // Log purchase attempt
                try {
                    var productJSON = args[1].readUtf8String();
                    console.log("[+] Purchase JSON: " + productJSON);
                } catch (e) {
                    console.log("[+] Could not read purchase JSON");
                }
            }
        });
        
        // Hook Initialize method to capture FakeStore instances
        var initializeAddr = baseAddress.add(RVA_ADDRESSES.INITIALIZE);
        Interceptor.attach(initializeAddr, {
            onEnter: function(args) {
                console.log("[+] FakeStore Initialize called");
                
                var fakeStorePtr = args[0];
                var billerPtr = args[1];
                
                // Register this FakeStore instance
                registerFakeStoreInstance(fakeStorePtr);
                
                // Patch for automation
                if (MEMORY_CONFIG.AUTO_PATCH) {
                    patchFakeStoreForAutomation(fakeStorePtr);
                }
            }
        });
        
        // Hook RestoreTransactions
        var restoreAddr = baseAddress.add(RVA_ADDRESSES.RESTORE_TRANSACTIONS);
        Interceptor.attach(restoreAddr, {
            onEnter: function(args) {
                console.log("[+] RestoreTransactions called");
                
                this.fakeStorePtr = args[0];
                this.callbackPtr = args[1];
            },
            onLeave: function(retval) {
                if (this.fakeStorePtr && MEMORY_CONFIG.AUTO_PATCH) {
                    // Set restore flag
                    this.fakeStorePtr.add(OFFSETS.RESTORE_CALLED).writeU8(1);
                    
                    // If we have a callback, trigger success
                    if (this.callbackPtr && !this.callbackPtr.isNull()) {
                        console.log("[+] Triggering restore success callback");
                        // Note: Actual callback invocation would need more analysis
                    }
                }
            }
        });
        
        console.log("[+] Method hooks installed successfully");
        
    } catch (error) {
        console.log("[-] Failed to set up method hooks: " + error.message);
    }
}

function scanForFakeStoreInstances() {
    console.log("[+] Scanning for FakeStore instances...");
    
    // Scan heap for potential FakeStore objects
    Process.enumerateRanges('rw-').forEach(function(range) {
        if (range.size < 0x1000) return; // Skip small ranges
        
        try {
            var current = range.base;
            var end = range.base.add(range.size);
            
            while (current.compare(end) < 0) {
                // Look for potential FakeStore vtable patterns
                try {
                    var vtablePtr = current.readPointer();
                    if (vtablePtr && isValidFakeStoreVTable(vtablePtr)) {
                        console.log("[+] Potential FakeStore found at: " + current);
                        registerFakeStoreInstance(current);
                    }
                } catch (e) {
                    // Ignore read errors
                }
                
                current = current.add(Process.pointerSize);
            }
        } catch (error) {
            // Ignore range scan errors
        }
    });
}

function isValidFakeStoreVTable(vtablePtr) {
    try {
        // Check if vtable contains expected method pointers
        var method1 = vtablePtr.readPointer();
        var method2 = vtablePtr.add(Process.pointerSize).readPointer();
        
        // Basic validation - methods should be in executable memory
        return method1 && method2 && 
               isExecutableAddress(method1) && 
               isExecutableAddress(method2);
    } catch (e) {
        return false;
    }
}

function isExecutableAddress(addr) {
    try {
        var range = Process.findRangeByAddress(addr);
        return range && range.protection.indexOf('x') !== -1;
    } catch (e) {
        return false;
    }
}

function registerFakeStoreInstance(ptr) {
    // Avoid duplicates
    for (var i = 0; i < fakeStoreInstances.length; i++) {
        if (fakeStoreInstances[i].equals(ptr)) {
            return;
        }
    }
    
    fakeStoreInstances.push(ptr);
    console.log("[+] Registered FakeStore instance: " + ptr);
    console.log("[+] Total instances: " + fakeStoreInstances.length);
}

function patchFakeStoreForAutomation(fakeStorePtr) {
    try {
        console.log("[+] Patching FakeStore for automation: " + fakeStorePtr);
        
        // Set UIMode to silent (0)
        fakeStorePtr.add(OFFSETS.UI_MODE).writeU32(0);
        
        // Initialize flags
        fakeStorePtr.add(OFFSETS.PURCHASE_CALLED).writeU8(0);
        fakeStorePtr.add(OFFSETS.RESTORE_CALLED).writeU8(0);
        
        console.log("[+] FakeStore patched for automation");
        
    } catch (error) {
        console.log("[-] Failed to patch FakeStore: " + error.message);
    }
}

function patchFakeStoreForSuccess(fakeStorePtr) {
    try {
        console.log("[+] Patching FakeStore for purchase success: " + fakeStorePtr);
        
        // Set purchase success flags
        fakeStorePtr.add(OFFSETS.PURCHASE_CALLED).writeU8(1);
        
        // Force UIMode to silent to avoid dialogs
        fakeStorePtr.add(OFFSETS.UI_MODE).writeU32(0);
        
        console.log("[+] FakeStore patched for success");
        
    } catch (error) {
        console.log("[-] Failed to patch FakeStore for success: " + error.message);
    }
}

// Utility functions for runtime manipulation
function patchAllInstances() {
    console.log("[+] Patching all known FakeStore instances...");
    
    fakeStoreInstances.forEach(function(instance) {
        patchFakeStoreForAutomation(instance);
    });
    
    console.log("[+] Patched " + fakeStoreInstances.length + " instances");
}

function forcePurchaseSuccess() {
    console.log("[+] Forcing purchase success on all instances...");
    
    fakeStoreInstances.forEach(function(instance) {
        patchFakeStoreForSuccess(instance);
    });
}

function dumpFakeStoreState(instancePtr) {
    try {
        console.log("[+] FakeStore State Dump for: " + instancePtr);
        console.log("    purchaseCalled: " + instancePtr.add(OFFSETS.PURCHASE_CALLED).readU8());
        console.log("    restoreCalled: " + instancePtr.add(OFFSETS.RESTORE_CALLED).readU8());
        console.log("    UIMode: " + instancePtr.add(OFFSETS.UI_MODE).readU32());
        
        // Try to read biller pointer
        var billerPtr = instancePtr.add(OFFSETS.M_BILLER).readPointer();
        console.log("    m_Biller: " + billerPtr);
        
    } catch (error) {
        console.log("[-] Failed to dump state: " + error.message);
    }
}

// Initialize the memory hooks
if (initializeMemoryHooks()) {
    console.log("[+] FakeStore Memory Hooks initialized successfully!");
    
    // Export utility functions
    global.patchAllFakeStores = patchAllInstances;
    global.forceFakeStorePurchaseSuccess = forcePurchaseSuccess;
    global.dumpFakeStoreState = dumpFakeStoreState;
    global.fakeStoreInstances = fakeStoreInstances;
    
    console.log("[+] Utility functions exported to global scope");
} else {
    console.log("[-] Failed to initialize FakeStore Memory Hooks");
}
