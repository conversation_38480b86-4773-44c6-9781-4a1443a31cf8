'use strict';

rpc.exports = {
    trigger: function (productJson, payload) {
        if (this.purchaseFunc == null || this.instance == null) {
            return "Purchase function or instance not ready.";
        }

        // Call Purchase manually
        this.purchaseFunc(this.instance, Memory.allocUtf8String(productJson), Memory.allocUtf8String(payload));
        return "Triggered purchase with: " + productJson;
    }
};

var baseAddr = Process.getModuleByName("libil2cpp.so").base;
var purchaseAddr = baseAddr.add(0x44967BC); // RVA

console.log("Purchase @ " + purchaseAddr);

var purchaseFunc = new NativeFunction(purchaseAddr, 'void', ['pointer', 'pointer', 'pointer']);

// Hook Purchase
Interceptor.attach(purchaseAddr, {
    onEnter: function (args) {
        console.log("== Purchase called ==");
        try {
            var product = Memory.readUtf8String(args[1]);
            var payload = Memory.readUtf8String(args[2]);
            console.log("productJSON: " + product);
            console.log("developerPayload: " + payload);

            // Save for replay
            rpc.exports.instance = args[0];
            rpc.exports.purchaseFunc = purchaseFunc;
        } catch (e) {
            console.log("Error reading args: " + e);
        }
    }
});
