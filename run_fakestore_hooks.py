#!/usr/bin/env python3
"""
Unity FakeStore Automation Runner
Launches Frida scripts to hook and automate Unity's FakeStore system
"""

import frida
import sys
import time
import json
import argparse
from pathlib import Path

class FakeStoreAutomation:
    def __init__(self, target_process, script_path=None):
        self.target_process = target_process
        self.session = None
        self.script = None
        self.script_path = script_path or "fakestore_automation.js"
        
    def attach_to_process(self):
        """Attach to target process"""
        try:
            print(f"[+] Attaching to process: {self.target_process}")
            
            if isinstance(self.target_process, int):
                self.session = frida.attach(self.target_process)
            else:
                self.session = frida.attach(self.target_process)
                
            print(f"[+] Successfully attached to {self.target_process}")
            return True
            
        except frida.ProcessNotFoundError:
            print(f"[-] Process '{self.target_process}' not found")
            return False
        except Exception as e:
            print(f"[-] Failed to attach: {e}")
            return False
    
    def load_script(self):
        """Load and execute the Frida script"""
        try:
            script_file = Path(self.script_path)
            if not script_file.exists():
                print(f"[-] Script file not found: {self.script_path}")
                return False
                
            print(f"[+] Loading script: {self.script_path}")
            
            with open(script_file, 'r', encoding='utf-8') as f:
                script_code = f.read()
            
            self.script = self.session.create_script(script_code)
            self.script.on('message', self.on_message)
            self.script.load()
            
            print("[+] Script loaded successfully")
            return True
            
        except Exception as e:
            print(f"[-] Failed to load script: {e}")
            return False
    
    def on_message(self, message, data):
        """Handle messages from Frida script"""
        if message['type'] == 'send':
            payload = message['payload']
            print(f"[SCRIPT] {payload}")
        elif message['type'] == 'error':
            print(f"[ERROR] {message['stack']}")
    
    def run_automation(self):
        """Main automation loop"""
        if not self.attach_to_process():
            return False
            
        if not self.load_script():
            return False
        
        print("[+] FakeStore automation is running...")
        print("[+] Commands:")
        print("    'status' - Show automation status")
        print("    'enable' - Enable automation")
        print("    'disable' - Disable automation") 
        print("    'purchase <id>' - Simulate purchase")
        print("    'quit' - Exit")
        print()
        
        try:
            while True:
                command = input("FakeStore> ").strip().lower()
                
                if command == 'quit' or command == 'exit':
                    break
                elif command == 'status':
                    self.get_status()
                elif command == 'enable':
                    self.enable_automation()
                elif command == 'disable':
                    self.disable_automation()
                elif command.startswith('purchase '):
                    product_id = command.split(' ', 1)[1]
                    self.simulate_purchase(product_id)
                elif command == 'help':
                    self.show_help()
                elif command:
                    print(f"[-] Unknown command: {command}")
                    
        except KeyboardInterrupt:
            print("\n[+] Shutting down...")
        
        return True
    
    def get_status(self):
        """Get automation status"""
        try:
            result = self.script.exports.get_automation_status()
            print("[+] Automation Status:")
            print(json.dumps(result, indent=2))
        except Exception as e:
            print(f"[-] Failed to get status: {e}")
    
    def enable_automation(self):
        """Enable automation"""
        try:
            self.script.exports.enable_automation()
            print("[+] Automation enabled")
        except Exception as e:
            print(f"[-] Failed to enable automation: {e}")
    
    def disable_automation(self):
        """Disable automation"""
        try:
            self.script.exports.disable_automation()
            print("[+] Automation disabled")
        except Exception as e:
            print(f"[-] Failed to disable automation: {e}")
    
    def simulate_purchase(self, product_id):
        """Simulate a purchase"""
        try:
            self.script.exports.simulate_purchase(product_id, "Consumable")
            print(f"[+] Simulated purchase: {product_id}")
        except Exception as e:
            print(f"[-] Failed to simulate purchase: {e}")
    
    def show_help(self):
        """Show help information"""
        print("""
[+] FakeStore Automation Commands:
    status              - Show current automation status
    enable              - Enable automatic purchase completion
    disable             - Disable automation (manual mode)
    purchase <id>       - Simulate purchase of product ID
    help                - Show this help message
    quit/exit           - Exit the automation tool

[+] The automation hooks the following:
    - Unity FakeStore purchase methods
    - Purchase dialog bypassing
    - Automatic success callbacks
    - Memory-based fallback hooks
        """)

def find_unity_processes():
    """Find running Unity processes"""
    unity_processes = []
    
    try:
        processes = frida.enumerate_processes()
        for process in processes:
            name = process.name.lower()
            if ('unity' in name or 
                'il2cpp' in name or 
                name.endswith('.exe') and 'game' in name):
                unity_processes.append(process)
    except Exception as e:
        print(f"[-] Failed to enumerate processes: {e}")
    
    return unity_processes

def main():
    parser = argparse.ArgumentParser(description='Unity FakeStore Automation Tool')
    parser.add_argument('target', nargs='?', help='Target process name or PID')
    parser.add_argument('-s', '--script', default='fakestore_automation.js', 
                       help='Frida script file to use')
    parser.add_argument('-l', '--list', action='store_true', 
                       help='List potential Unity processes')
    parser.add_argument('--memory-only', action='store_true',
                       help='Use memory-only hooks script')
    
    args = parser.parse_args()
    
    if args.list:
        print("[+] Scanning for Unity processes...")
        processes = find_unity_processes()
        
        if processes:
            print("[+] Potential Unity processes found:")
            for i, proc in enumerate(processes):
                print(f"    {i+1}. {proc.name} (PID: {proc.pid})")
        else:
            print("[-] No Unity processes found")
        return
    
    if not args.target:
        print("[-] No target specified. Use -l to list processes or provide process name/PID")
        return
    
    # Determine script to use
    script_path = args.script
    if args.memory_only:
        script_path = "fakestore_memory_hooks.js"
    
    # Convert PID if numeric
    target = args.target
    if target.isdigit():
        target = int(target)
    
    # Create and run automation
    automation = FakeStoreAutomation(target, script_path)
    
    if automation.run_automation():
        print("[+] Automation completed successfully")
    else:
        print("[-] Automation failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
