# Unity FakeStore Automation

This toolkit provides comprehensive automation for Unity's In-App Purchasing FakeStore system, enabling automatic purchase completion and bypassing purchase dialogs for testing and automation purposes.

## Files Overview

### Core Scripts

1. **`fakestore_automation.js`** - Main comprehensive automation script
   - Hooks FakeStore class methods
   - Provides automatic purchase completion
   - Bypasses purchase dialogs
   - Includes fallback mechanisms

2. **`frida_fakestore_hooks.js`** - Java-based hooks with configuration
   - Configurable automation behavior
   - Detailed logging options
   - Runtime configuration modification

3. **`fakestore_memory_hooks.js`** - Memory-based hooks for when Java hooks fail
   - Direct memory manipulation
   - RVA-based method hooking
   - Instance scanning and patching

4. **`run_fakestore_hooks.py`** - Python runner script
   - Easy process attachment
   - Interactive command interface
   - Process discovery utilities

## Quick Start

### 1. Basic Usage

```bash
# List potential Unity processes
python run_fakestore_hooks.py -l

# Attach to a process by name
python run_fakestore_hooks.py "YourGameName"

# Attach to a process by PID
python run_fakestore_hooks.py 1234
```

### 2. Direct Frida Usage

```bash
# Load main automation script
frida -U -f com.your.game -l fakestore_automation.js

# Load memory-only hooks
frida -U -f com.your.game -l fakestore_memory_hooks.js
```

## Features

### Automatic Purchase Completion
- Intercepts all purchase attempts
- Automatically completes purchases without payment
- Generates fake receipts and transaction IDs
- Triggers success callbacks immediately

### Dialog Bypassing
- Skips purchase confirmation dialogs
- Sets UIMode to silent operation
- Auto-approves all purchase prompts

### Multi-Store Support
- Hooks FakeStore directly
- Redirects real store purchases (Google Play, App Store) to FakeStore behavior
- Comprehensive store selection override

### Memory-Level Hooks
- Direct memory manipulation when Java hooks fail
- RVA-based method interception
- Instance scanning and patching

## Configuration

### Runtime Configuration (fakestore_automation.js)

```javascript
// Access global configuration
global.FakeStoreAutomation.config.AUTO_COMPLETE_PURCHASES = true;
global.FakeStoreAutomation.config.BYPASS_PURCHASE_DIALOGS = true;

// Enable/disable automation
global.FakeStoreAutomation.enable();
global.FakeStoreAutomation.disable();

// Simulate purchases
global.FakeStoreAutomation.simulatePurchase("premium_currency_100", "Consumable");
```

### Memory Hook Configuration (fakestore_memory_hooks.js)

```javascript
// Patch all known instances
global.patchAllFakeStores();

// Force purchase success
global.forceFakeStorePurchaseSuccess();

// Dump instance state
global.dumpFakeStoreState(instancePointer);
```

## Interactive Commands

When using the Python runner, you have access to these commands:

- `status` - Show current automation status
- `enable` - Enable automatic purchase completion
- `disable` - Disable automation
- `purchase <product_id>` - Simulate a purchase
- `help` - Show help information
- `quit` - Exit the tool

## Technical Details

### FakeStore Class Structure

The Unity FakeStore class contains these key components:

```csharp
// Key fields (memory offsets)
private IStoreCallback m_Biller;           // 0x60
private List<string> m_PurchasedProducts;  // 0x68
public bool purchaseCalled;                // 0x70
public bool restoreCalled;                 // 0x71
public FakeStoreUIMode UIMode;             // 0x80
```

### Hook Points

1. **Purchase Flow**
   - `Purchase(string productJSON, string developerPayload)`
   - `FakePurchase(ProductDefinition product, string developerPayload)`
   - Async callback handlers

2. **UI System**
   - `StartUI<T>(object model, DialogType dialogType, Action<bool, T> callback)`
   - UIMode property manipulation

3. **Store Selection**
   - Unity IAP initialization hooks
   - Store factory method overrides

## Security Implications

⚠️ **Warning**: This toolkit is designed for testing and automation purposes. Using it on production applications may violate terms of service and could be considered fraud.

### Legitimate Uses
- Game development testing
- QA automation
- Educational purposes
- Security research

### Potential Misuse
- Bypassing legitimate in-app purchases
- Fraud in production games
- Terms of service violations

## Troubleshooting

### Common Issues

1. **Java hooks not working**
   - Try the memory-only script: `fakestore_memory_hooks.js`
   - Check if the game uses obfuscation

2. **Process not found**
   - Use `-l` flag to list processes
   - Ensure the game is running
   - Try different process names

3. **Hooks not triggering**
   - Verify the game uses Unity IAP
   - Check if FakeStore is enabled in the build
   - Try memory-based hooks as fallback

### Debug Mode

Enable detailed logging by modifying the configuration:

```javascript
const CONFIG = {
    LOG_PURCHASES: true,
    LOG_MEMORY_OPERATIONS: true,
    // ... other options
};
```

## Advanced Usage

### Custom Product Simulation

```javascript
// Simulate specific product types
global.FakeStoreAutomation.simulatePurchase("premium_pack_1", "NonConsumable");
global.FakeStoreAutomation.simulatePurchase("monthly_subscription", "Subscription");
```

### Memory Manipulation

```javascript
// Direct memory patching
var fakeStorePtr = ptr("0x12345678"); // Your FakeStore instance address
fakeStorePtr.add(0x80).writeU32(0);   // Set UIMode to silent
fakeStorePtr.add(0x70).writeU8(1);    // Set purchaseCalled flag
```

## Requirements

- Frida framework
- Python 3.6+ (for runner script)
- Target application with Unity IAP
- Root/jailbreak access (for mobile targets)

## Legal Notice

This toolkit is provided for educational and testing purposes only. Users are responsible for ensuring compliance with applicable laws and terms of service. The authors are not responsible for any misuse of this software.
